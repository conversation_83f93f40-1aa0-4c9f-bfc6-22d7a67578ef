"use client"; // Add this directive for client-side hooks

import React, { createContext, useContext, useState, useEffect, ReactNode, useMemo, useCallback } from 'react';
// import { createClientComponentClient } from "@supabase/auth-helpers-nextjs"; // OLD
import { createSupabaseBrowserClient } from '@/lib/supabase/client'; // NEW
import type { SupabaseClient, Session, AuthChangeEvent } from '@supabase/supabase-js';
// import { useRouter } from 'next/navigation'; // Not directly used, router.push handled in components
import { syncUserProfileAPI } from '@/services/api'; // Adjust path
import { PrismaUserProfile } from '@/types/user'; // Import from local type definition

export interface AuthContextType {
  supabase: SupabaseClient; // Use generic SupabaseClient or your specific Database type
  session: Session | null;
  user: PrismaUserProfile | null; // Changed back to PrismaUserProfile
  isLoading: boolean;
  setSession: (session: Session | null) => void; // Allow setting session directly
  authError: string | null; // Added for auth errors
  setAuthError: (error: string | null) => void; // Added for setting auth errors
  logout: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  // const router = useRouter(); // Not directly used here
  const supabase = useMemo(() => createSupabaseBrowserClient(), []); // NEW: Memoize client
  const [user, setUser] = useState<PrismaUserProfile | null>(null); // Changed back to PrismaUserProfile
  const [session, setSessionState] = useState<Session | null>(null); // This is the session we want to pass
  const [isLoading, setIsLoading] = useState(true);
  const [authError, setAuthError] = useState<string | null>(null); // Added for auth errors
  const [isHydrated, setIsHydrated] = useState(false); // Track hydration state
  const [initialAuthCheck, setInitialAuthCheck] = useState(false); // Track if initial auth check is done

  // console.log("[AuthContext] AuthProvider rendering. Loading:", isLoading, "User:", !!user, "Session:", !!session); // Reduced verbosity

  const handleSyncProfile = useCallback(async (currentSession: Session | null) => {
    // console.log('[AuthContext] handleSyncProfile called with session:', currentSession ? "Exists" : "null"); // Keep if essential for debugging sync issues
    if (!currentSession) { // Early exit if no session
      // console.log('[AuthContext] handleSyncProfile: No session, setting user to null and stopping load.'); // Keep if essential
      setUser(null);
      setIsLoading(false);
      return;
    }
    setIsLoading(true);
    try {
      const backendProfile = await syncUserProfileAPI(currentSession);
      // console.log('[AuthContext] Profile synced:', backendProfile); // Keep if essential
      setUser(backendProfile);
      setAuthError(null);
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      console.error('[AuthContext] Error syncing profile:', errorMessage);
      setAuthError(`Profile sync failed: ${errorMessage}`);
      setUser(null); // Clear user on sync error
    } finally {
      setIsLoading(false);
    }
  }, []); // No dependencies needed as syncUserProfileAPI is stable and currentSession is a param

  // Hydration effect
  useEffect(() => {
    setIsHydrated(true);
  }, []);

  useEffect(() => {
    // Don't start auth flow until hydrated
    if (!isHydrated) return;

    // console.log('[AuthContext] Setting up onAuthStateChange listener.'); // Keep if essential for init debug

    const getInitialSession = async () => {
      console.log('[AuthContext] getInitialSession() called');
      const { data: { session: initialSession }, error: initialSessionError } = await supabase.auth.getSession();

      if (initialSessionError) {
        console.error('[AuthContext] Error getting initial session:', initialSessionError.message);
        setAuthError(`Failed to get initial session: ${initialSessionError.message}`);
        setIsLoading(false);
        setInitialAuthCheck(true);
        return;
      }

      console.log('[AuthContext] Initial getSession() result:', initialSession ? "Session exists" : "No session");
      if (initialSession) {
        console.log('[AuthContext] Session details - User ID:', initialSession.user?.id, 'Access token exists:', !!initialSession.access_token);
      }
      setSessionState(initialSession); // Set session state

      if (!initialSession) {
          console.log('[AuthContext] No initial session, setting isLoading to false and user to null.');
          setUser(null);
          setIsLoading(false);
          setInitialAuthCheck(true);
      } else {
          // If we have a session, sync the profile
          console.log('[AuthContext] Session found, attempting to sync profile...');
          await handleSyncProfile(initialSession);
          setInitialAuthCheck(true);
      }
      // If initialSession exists, onAuthStateChange may fire with INITIAL_SESSION (or SIGNED_IN if recently logged in)
      // which will then trigger handleSyncProfile. If not, loading is set to false above.
    };

    getInitialSession();

    const { data: authListener } = supabase.auth.onAuthStateChange(
      async (event: AuthChangeEvent, newSession: Session | null) => {
        // Skip initial session event if we already handled it
        if (event === 'INITIAL_SESSION' && initialAuthCheck) {
          return;
        }

        console.log(`[AuthContext] onAuthStateChange event: ${event}`, newSession ? "session data exists" : "no session data");
        setSessionState(newSession);

        const isSyncEvent =
          (event === 'SIGNED_IN') ||
          (event === 'TOKEN_REFRESHED') ||
          (event === 'USER_UPDATED' && !!newSession?.user);

        if (isSyncEvent) {
            await handleSyncProfile(newSession);
        } else if (event === 'SIGNED_OUT' || (event as string) === 'USER_DELETED') {
          setUser(null);
          setIsLoading(false);
        } else if (event === 'PASSWORD_RECOVERY' || event === 'MFA_CHALLENGE_VERIFIED'){
            setIsLoading(false);
        } else if (!newSession && event !== 'INITIAL_SESSION') {
            setUser(null);
            setIsLoading(false);
        }

        if (typeof window !== 'undefined' && (window.location.hash.includes('access_token') || window.location.hash.includes('error_description'))) {
          window.history.replaceState({}, document.title, window.location.pathname + window.location.search);
        }
      }
    );

    return () => {
      // console.log('[AuthContext] Unsubscribing from auth state changes.'); // Keep if essential
      if (authListener?.subscription) {
        authListener.subscription.unsubscribe();
      }
    };
  }, [supabase, handleSyncProfile, isHydrated, initialAuthCheck]); // handleSyncProfile is memoized, supabase is stable

  const logout = useCallback(async () => {
    // console.log('[AuthContext] logout() called'); // Keep if essential
    setIsLoading(true); // Indicate loading during logout process
    setAuthError(null);
    const { error } = await supabase.auth.signOut();
    if (error) {
      console.error("[AuthContext] Error logging out:", error.message);
      setAuthError(error.message);
      // If signOut itself fails, we might not get a SIGNED_OUT event, so manually stop loading.
      setIsLoading(false); 
    }
    // onAuthStateChange will handle setting user/session to null and potentially setIsLoading(false) via 'SIGNED_OUT' event.
    // However, setting isLoading(false) in case of error provides immediate feedback.
  }, [supabase]);

  const setSessionCallback = useCallback((newSession: Session | null) => {
    setSessionState(newSession);
    if (newSession?.user) {
         setUser({
            // Attempt to match PrismaUserProfile structure more closely from Supabase user object
            // Note: This is a *constructed* profile. Full data comes from syncUserProfileAPI.
            id: newSession.user.id, // This is Supabase User UUID, map to Prisma id if it's the same key
            auth_user_id: newSession.user.id, // Explicitly from Supabase User
            email: newSession.user.email || null,
            display_name: newSession.user.user_metadata?.display_name || newSession.user.user_metadata?.displayName || newSession.user.email?.split('@')[0] || null,
            username: newSession.user.user_metadata?.username || null,
            role: (newSession.user.app_metadata?.userrole as PrismaUserProfile['role']) || 'USER',
            avatar_url: newSession.user.user_metadata?.avatar_url || newSession.user.user_metadata?.avatarUrl || null,
            bio: newSession.user.user_metadata?.bio || null,
            location: newSession.user.user_metadata?.location || null,
            website_url: newSession.user.user_metadata?.website_url || newSession.user.user_metadata?.websiteUrl || null,
            github_url: newSession.user.user_metadata?.github_url || newSession.user.user_metadata?.githubUrl || null,
            linkedin_url: newSession.user.user_metadata?.linkedin_url || newSession.user.user_metadata?.linkedinUrl || null,
            status: newSession.user.email_confirmed_at ? 'ACTIVE' : 'PENDING', // Example mapping
            // Fields not typically in Supabase user object, set to default/null
            technical_level: null,
            learning_goals: null,
            preferred_content_types: null,
            hours_per_week: null,
            followed_tags: [],
            followed_categories: [],
            notification_preferences_id: null, // Cannot derive this from Supabase user alone
            notificationPreferences: null, // Cannot derive this from Supabase user alone
            created_at: new Date(newSession.user.created_at),
            updated_at: new Date(newSession.user.updated_at || newSession.user.created_at),
         } as unknown as PrismaUserProfile); // Reverted to 'as unknown as PrismaUserProfile' for pragmatic type assertion
    } else {
        setUser(null);
    }
    setIsLoading(false);
    if (newSession) setAuthError(null);
  }, []);

  const value = useMemo(() => ({
    supabase,
    session,
    user,
    isLoading,
    setSession: setSessionCallback,
    authError,
    setAuthError,
    logout,
  }), [supabase, session, user, isLoading, setSessionCallback, authError, setAuthError, logout]);

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}; 